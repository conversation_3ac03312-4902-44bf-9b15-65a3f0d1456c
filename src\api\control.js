/**
 * 控制面板相关的API接口
 * 提供Modbus设备控制和状态查询的接口封装
 */

import { post } from '@/utils/request'
import { ElMessage } from 'element-plus'

/**
 * 执行Modbus控制命令
 * @param {number} pointId - 点位ID
 * @param {number|boolean} value - 控制值
 * @param {string} description - 操作描述
 * @returns {Promise<boolean>} 执行结果
 */
export const executeModbusCommand = async (pointId, value, description) => {
  try {
    const response = await post('/qingma/modbusSite/modbusCmd', {
      pointId,
      value,
      cmdType: 'WRITE',
      description
    })
    
    if (response.success) {
      ElMessage.success('控制命令执行成功')
      return true
    } else {
      ElMessage.error(`控制命令执行失败: ${response.message}`)
      return false
    }
  } catch (error) {
    console.error('执行控制命令失败:', error)
    ElMessage.error('网络错误，请检查连接')
    return false
  }
}

/**
 * 批量查询点位状态
 * @param {number[]} pointIds - 点位ID数组
 * @returns {Promise<Array>} 点位状态数据
 */
export const getPointsStatus = async (pointIds) => {
  try {
    const response = await post('/qingma/modbusSite/pointStatus', {
      pointIds
    })
    
    if (response.success) {
      return response.data || []
    }
    return []
  } catch (error) {
    console.error('获取点位状态失败:', error)
    return []
  }
}

/**
 * 读取单个点位状态
 * @param {number} pointId - 点位ID
 * @param {string} description - 操作描述
 * @returns {Promise<any>} 点位值
 */
export const readModbusPoint = async (pointId, description) => {
  try {
    const response = await post('/qingma/modbusSite/modbusCmd', {
      pointId,
      value: 0, // 读取操作时值可以为任意
      cmdType: 'READ',
      description
    })
    
    if (response.success) {
      return response.data
    }
    return null
  } catch (error) {
    console.error('读取点位失败:', error)
    return null
  }
}

// 控制面板专用API方法

/**
 * 切换操作模式
 * @param {number} mode - 0: 手动, 1: 自动
 * @param {number} pointId - 点位ID
 * @returns {Promise<boolean>}
 */
export const setOperationMode = async (mode, pointId) => {
  return await executeModbusCommand(
    pointId,
    mode,
    `切换到${mode ? '自动' : '手动'}模式`
  )
}

/**
 * 控制变频器启停
 * @param {number} status - 0: 停止, 1: 启动
 * @param {number} pointId - 点位ID
 * @returns {Promise<boolean>}
 */
export const setInverterStatus = async (status, pointId) => {
  return await executeModbusCommand(
    pointId,
    status,
    `${status ? '启动' : '停止'}变频器`
  )
}

/**
 * 变频器故障复位
 * @param {number} pointId - 点位ID
 * @returns {Promise<boolean>}
 */
export const resetInverter = async (pointId) => {
  return await executeModbusCommand(
    pointId,
    1,
    '变频器故障复位'
  )
}

/**
 * 手动控制设备
 * @param {number} pointId - 点位ID
 * @param {number} value - 控制值
 * @param {string} deviceName - 设备名称
 * @returns {Promise<boolean>}
 */
export const setManualControl = async (pointId, value, deviceName) => {
  return await executeModbusCommand(
    pointId,
    value,
    `手动控制${deviceName}: ${value ? '开启' : '关闭'}`
  )
}

/**
 * 设置风速
 * @param {number} speed - 风速百分比 (0-100)
 * @param {number} pointId - 点位ID
 * @returns {Promise<boolean>}
 */
export const setFanSpeed = async (speed, pointId) => {
  return await executeModbusCommand(
    pointId,
    speed,
    `设置风速为${speed}%`
  )
}

/**
 * 设置温度
 * @param {number} temperature - 温度值 (°C)
 * @param {number} pointId - 点位ID
 * @returns {Promise<boolean>}
 */
export const setTemperature = async (temperature, pointId) => {
  return await executeModbusCommand(
    pointId,
    temperature,
    `设置温度为${temperature}°C`
  )
}

/**
 * 设置湿度
 * @param {number} humidity - 湿度值 (%)
 * @param {number} pointId - 点位ID
 * @returns {Promise<boolean>}
 */
export const setHumidity = async (humidity, pointId) => {
  return await executeModbusCommand(
    pointId,
    humidity,
    `设置湿度为${humidity}%`
  )
}

/**
 * 设置运行模式
 * @param {number} mode - 运行模式
 * @param {number} pointId - 点位ID
 * @param {string} modeText - 模式文本描述
 * @returns {Promise<boolean>}
 */
export const setRunningMode = async (mode, pointId, modeText) => {
  return await executeModbusCommand(
    pointId,
    mode,
    `设置运行模式为${modeText}`
  )
}

/**
 * 设置开放能需
 * @param {number} energyDemand - 能需值 (kW)
 * @param {number} pointId - 点位ID
 * @returns {Promise<boolean>}
 */
export const setEnergyDemand = async (energyDemand, pointId) => {
  return await executeModbusCommand(
    pointId,
    energyDemand,
    `设置开放能需为${energyDemand}kW`
  )
}

export default {
  executeModbusCommand,
  getPointsStatus,
  readModbusPoint,
  setOperationMode,
  setInverterStatus,
  resetInverter,
  setManualControl,
  setFanSpeed,
  setTemperature,
  setHumidity,
  setRunningMode,
  setEnergyDemand
}
