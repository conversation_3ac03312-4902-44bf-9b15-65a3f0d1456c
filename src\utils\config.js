/**
 * 应用配置文件
 * 提供环境变量的统一访问和配置管理
 */

// 环境变量配置
export const ENV_CONFIG = {
  // 应用基本信息
  APP_TITLE: import.meta.env.VITE_APP_TITLE || '青马大桥除湿监测系统',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  
  // 环境信息
  NODE_ENV: import.meta.env.NODE_ENV || 'development',
  
  // 数据源配置
  DATA_SOURCE: import.meta.env.VITE_DATA_SOURCE || 'mock',
  
  // API 配置
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/api/v1',
  
  // WebSocket 配置 (SockJS需要使用HTTP协议)
  WS_URL: import.meta.env.VITE_WS_URL || 'http://localhost:8080/ws',
  
  // 调试模式
  DEBUG: import.meta.env.VITE_DEBUG === 'true',
  
  // bim地址
  BIM_URL: import.meta.env.VITE_BIM_URL || 'http://127.0.0.1:5002/gisBimH5/bimQingmaMaintain.html',
}

// 数据源类型
export const DATA_SOURCE_TYPES = {
  MOCK: 'mock',
  API: 'api'
}

// 应用配置
export const APP_CONFIG = {
  // 默认阈值配置
  DEFAULT_THRESHOLDS: {
    humidity: {
      warning: { min: 30, max: 80 },
      danger: { min: 20, max: 90 }
    },
    pressure: {
      warning: { min: 1005, max: 1020 },
      danger: { min: 1000, max: 1025 }
    },
    temperature: {
      warning: { min: 18, max: 28 },
      danger: { min: 15, max: 32 }
    },
    flow: {
      warning: { min: 100, max: 150 },
      danger: { min: 80, max: 180 }
    }
  },
  
  // 监测点位配置
  MONITOR_POINTS: [
    {
      id: '1',
      title: 'Exhaust Sleeve（No. 49）',
      primaryDataType: 'humidity', // 主要显示的数据类型
      dataItems: ['humidity', 'temperature', 'flow', 'pressure']
    },
    {
      id: '2',
      title: 'Injection Sleeve（No. 57）',
      primaryDataType: 'humidity',
      dataItems: ['humidity', 'temperature', 'flow', 'pressure']
    },
    {
      id: '3',
      title: 'Plant Room（No. 61）',
      primaryDataType: 'humidity',
      dataItems: ['humidity', 'temperature', 'flow', 'pressure']
    },
    {
      id: '4',
      title: 'Exhaust Sleeve（No. 66）',
      primaryDataType: 'humidity',
      dataItems: ['humidity', 'temperature', 'flow', 'pressure']
    }
  ],
  
  // 图表配置
  CHART_CONFIG: {
    // 默认时间范围选项
    TIME_RANGES: [
      { label: '最近1小时', value: '1h', hours: 1 },
      { label: '最近6小时', value: '6h', hours: 6 },
      { label: '最近12小时', value: '12h', hours: 12 },
      { label: '最近24小时', value: '24h', hours: 24 },
      { label: '最近3天', value: '3d', hours: 72 },
      { label: '最近7天', value: '7d', hours: 168 }
    ],
    
    // 图表主题色彩
    COLORS: {
      primary: '#00a2ff',
      secondary: '#00ff88',
      warning: '#ffaa00',
      danger: '#ff4757',
      humidity: '#00a2ff',
      pressure: '#ff6b35',
      temperature: '#00ff88',
      flow: '#ffd700'
    }
  },
  
  // WebSocket 配置
  WS_CONFIG: {
    // 重连配置
    RECONNECT_INTERVAL: 3000,
    MAX_RECONNECT_ATTEMPTS: 5,
    
    // 心跳配置
    HEARTBEAT_INTERVAL: 30000,
    
    // 事件类型
    EVENTS: {
      CONNECTED: 'connected',
      DISCONNECTED: 'disconnected',
      ERROR: 'error',
      MESSAGE: 'message',
      REAL_TIME_DATA: 'realTimeData',
      DEVICE_STATUS: 'deviceStatus',
      ALARM: 'alarm',
      RECONNECT_FAILED: 'reconnectFailed'
    }
  },
  
  // 用户配置
  USER_CONFIG: {
    // 默认用户信息
    DEFAULT_USER: {
      username: 'admin',
      password: '123456'
    },

    // Token 存储键名
    TOKEN_KEY: 'token',
    REFRESH_TOKEN_KEY: 'refreshToken',
    USER_INFO_KEY: 'userInfo'
  },

  // 控制面板配置
  CONTROL_PANEL_CONFIG: {
    // 点位ID映射配置
    POINT_IDS: {
      // 基础控制点位
      OPERATION_MODE: 1,        // 操作模式：0-手动，1-自动
      RUNNING_STATUS: 2,        // 运行状态：0-停止，1-运行
      INVERTER_STATUS: 3,       // 变频器启停：0-停止，1-启动
      INVERTER_RESET: 4,        // 变频器故障复位

      // 手动控制点位
      BYPASS_VALVE: 5,          // 旁路风阀：0-关闭，1-开启
      AIR_PREPROCESSOR: 6,      // 空气预处理器：0-停止，1-启动
      DEHUMIDIFIER: 7,          // 除湿机：0-停止，1-启动
      INLET_VALVE: 8,           // 进风阀：0-关闭，1-开启
      OUTLET_VALVE: 9,          // 出风口风阀：0-关闭，1-开启
      PREPROCESSOR_FAN: 10,     // 空气预处理器风机：0-停止，1-启动
      FAN_SPEED: 11,            // 空气预处理器风速调节：0-100%

      // 参数设置点位
      TEMPERATURE_SET: 12,      // 温度设定：0-50°C
      HUMIDITY_SET: 13,         // 湿度设定：0-100%
      MODE_SET: 14,             // 模式设定：1-制冷，2-制热，3-热源制热，4-通风，6-自动
      ENERGY_DEMAND_SET: 15     // 开放能需设定：0-1000kW
    },

    // 模式设定选项
    MODE_OPTIONS: [
      { label: '制冷运行', value: 1 },
      { label: '制热运行', value: 2 },
      { label: '热源制热运行', value: 3 },
      { label: '通风运行', value: 4 },
      { label: '自动', value: 6 }
    ],

    // 设备类型映射
    DEVICE_TYPES: {
      bypassValve: { pointId: 5, name: '旁路风阀' },
      airPreprocessor: { pointId: 6, name: '空气预处理器' },
      dehumidifier: { pointId: 7, name: '除湿机' },
      inletValve: { pointId: 8, name: '进风阀' },
      outletValve: { pointId: 9, name: '出风口风阀' },
      preprocessorFan: { pointId: 10, name: '预处理器风机' }
    },

    // 状态刷新间隔（毫秒）
    STATUS_REFRESH_INTERVAL: 5000,

    // 默认参数值
    DEFAULT_PARAMETERS: {
      temperature: 25.0,
      humidity: 60.0,
      mode: 6,
      energyDemand: 500,
      fanSpeed: 50
    }
  }
}

// 工具函数
export const isProduction = () => ENV_CONFIG.NODE_ENV === 'production'
export const isDevelopment = () => ENV_CONFIG.NODE_ENV === 'development'
export const isMockMode = () => ENV_CONFIG.DATA_SOURCE === DATA_SOURCE_TYPES.MOCK
export const isApiMode = () => ENV_CONFIG.DATA_SOURCE === DATA_SOURCE_TYPES.API
export const isDebugMode = () => ENV_CONFIG.DEBUG

// 获取监测点位信息
export const getMonitorPoint = (id) => {
  return APP_CONFIG.MONITOR_POINTS.find(point => point.id === id)
}

// 获取阈值配置
export const getThresholdConfig = (dataType) => {
  return APP_CONFIG.DEFAULT_THRESHOLDS[dataType] || APP_CONFIG.DEFAULT_THRESHOLDS.humidity
}

// 获取图表颜色
export const getChartColor = (dataType) => {
  return APP_CONFIG.CHART_CONFIG.COLORS[dataType] || APP_CONFIG.CHART_CONFIG.COLORS.primary
}

// 打印配置信息（调试用）
export const printConfig = () => {
  if (isDebugMode()) {
    console.group('🔧 应用配置信息')
    console.log('环境:', ENV_CONFIG.NODE_ENV)
    console.log('数据源:', ENV_CONFIG.DATA_SOURCE)
    console.log('API地址:', ENV_CONFIG.API_BASE_URL)
    console.log('WebSocket地址:', ENV_CONFIG.WS_URL)
    console.log('调试模式:', ENV_CONFIG.DEBUG)
    console.groupEnd()
  }
}

export default {
  ENV_CONFIG,
  APP_CONFIG,
  DATA_SOURCE_TYPES,
  isProduction,
  isDevelopment,
  isMockMode,
  isApiMode,
  isDebugMode,
  getMonitorPoint,
  getThresholdConfig,
  getChartColor,
  printConfig
}
