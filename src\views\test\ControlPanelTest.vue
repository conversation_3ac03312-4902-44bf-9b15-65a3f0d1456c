<template>
  <div class="control-panel-test-container">
    <!-- 页面标题和控制区域 -->
    <div class="test-header">
      <h2 class="test-title">ControlPanel 组件测试页面</h2>
      <div class="test-controls">
        <div class="control-item">
          <label>选择尺寸:</label>
          <el-select v-model="selectedSize" @change="handleSizeChange" placeholder="请选择尺寸">
            <el-option
              v-for="option in sizeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </div>
        <div class="size-info">
          <span>当前尺寸: {{ currentSizeInfo }}</span>
        </div>
      </div>
    </div>

    <!-- 测试内容区域 -->
    <div class="test-content">
      <div class="test-panel" :class="selectedSize">
        <ControlPanel :title="currentTitle" />
      </div>
    </div>

    
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import ControlPanel from '@/components/ControlPanel.vue'

// 尺寸选项配置
const sizeOptions = [
  { label: 'Dashboard 左侧面板 (340px × 600px)', value: 'dashboard-left', width: '340px', height: '600px' },
  { label: 'Dashboard 右侧面板 (360px × 600px)', value: 'dashboard-right', width: '360px', height: '600px' },
  { label: '标准尺寸 (500px × 650px)', value: 'standard', width: '500px', height: '650px' },
  { label: '紧凑尺寸 (400px × 550px)', value: 'compact', width: '400px', height: '550px' },
  { label: '移动端尺寸 (320px × 480px)', value: 'mobile', width: '320px', height: '480px' },
  { label: '超大尺寸 (600px × 750px)', value: 'large', width: '600px', height: '750px' }
]

// 当前选中的尺寸
const selectedSize = ref('dashboard-left')

// 当前尺寸信息
const currentSizeInfo = computed(() => {
  const option = sizeOptions.find(opt => opt.value === selectedSize.value)
  return option ? `${option.width} × ${option.height}` : ''
})

// 当前标题
const currentTitle = computed(() => {
  const option = sizeOptions.find(opt => opt.value === selectedSize.value)
  return option ? option.label.split(' (')[0] : 'ControlPanel 测试'
})

// 处理尺寸变化
const handleSizeChange = (value) => {
  console.log('切换到尺寸:', value)
}
</script>

<style scoped>
.control-panel-test-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
  padding: 20px;
  color: #ffffff;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(0, 162, 255, 0.1);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
}

.test-title {
  color: #00a2ff;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 20px;
}

.test-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-item label {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.size-info {
  color: #00a2ff;
  font-size: 14px;
  font-weight: 500;
}

.test-content {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin-bottom: 30px;
  min-height: 600px;
}

/* 测试面板 */
.test-panel {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s ease;
}

/* 不同尺寸的测试面板 */
.test-panel.dashboard-left {
  width: 340px;
  height: 600px;
}

.test-panel.dashboard-right {
  width: 360px;
  height: 600px;
}

.test-panel.standard {
  width: 500px;
  height: 650px;
}

.test-panel.compact {
  width: 400px;
  height: 550px;
}

.test-panel.mobile {
  width: 320px;
  height: 480px;
}

.test-panel.large {
  width: 600px;
  height: 750px;
}

/* 功能说明样式 */
.feature-info {
  background: rgba(0, 162, 255, 0.1);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.feature-info h3 {
  color: #00a2ff;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
  text-align: center;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.feature-item {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 162, 255, 0.2);
  border-radius: 6px;
  padding: 15px;
}

.feature-item h4 {
  color: #00a2ff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.feature-item p {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  opacity: 0.9;
}

/* Element Plus 组件样式覆盖 */
:deep(.el-select) {
  width: 280px;
}

:deep(.el-select .el-input__wrapper) {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(0, 162, 255, 0.3) !important;
}

:deep(.el-select .el-input__inner) {
  color: #ffffff !important;
}

:deep(.el-select .el-input__suffix) {
  color: #00a2ff !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .test-panel.dashboard-left {
    width: 300px;
    height: 550px;
  }

  .test-panel.dashboard-right {
    width: 320px;
    height: 550px;
  }

  .test-panel.standard {
    width: 450px;
    height: 600px;
  }

  .test-panel.compact {
    width: 350px;
    height: 500px;
  }

  .test-panel.large {
    width: 500px;
    height: 650px;
  }
}

@media (max-width: 768px) {
  .control-panel-test-container {
    padding: 15px;
  }

  .test-controls {
    flex-direction: column;
    gap: 15px;
  }

  .test-panel {
    width: 100% !important;
    max-width: 400px;
    height: 500px !important;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .control-panel-test-container {
    padding: 10px;
  }

  .test-header {
    padding: 15px;
  }

  .test-title {
    font-size: 24px;
  }

  .test-panel {
    max-width: 320px;
    height: 450px !important;
  }

  .feature-info {
    padding: 15px;
  }

  .feature-info h3 {
    font-size: 18px;
  }

  :deep(.el-select) {
    width: 100%;
  }
}
</style>
