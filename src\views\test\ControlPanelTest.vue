<template>
  <div class="control-panel-test-container">
    <!-- 页面标题 -->
    <div class="test-header">
      <h2 class="test-title">ControlPanel 组件测试页面</h2>
      <div class="test-info">
        <span>测试不同尺寸和配置的 ControlPanel 组件</span>
      </div>
    </div>

    <!-- 测试内容区域 -->
    <div class="test-content">
      <!-- 左侧面板 - 模拟 Dashboard 左侧布局 -->
      <div class="test-left-panels">
        <div class="test-panel">
          <h3 class="panel-title">左侧面板尺寸 (340px)</h3>
          <ControlPanel title="除湿系统控制面板" />
        </div>
      </div>

      <!-- 中间区域 - 展示不同尺寸 -->
      <div class="test-center-area">
        <!-- 标准尺寸测试 -->
        <div class="test-section">
          <h3 class="section-title">标准尺寸测试 (500px × 600px)</h3>
          <div class="test-panel standard-size">
            <ControlPanel title="标准尺寸控制面板" />
          </div>
        </div>

        <!-- 紧凑尺寸测试 -->
        <div class="test-section">
          <h3 class="section-title">紧凑尺寸测试 (400px × 500px)</h3>
          <div class="test-panel compact-size">
            <ControlPanel title="紧凑尺寸控制面板" />
          </div>
        </div>

        <!-- 移动端尺寸测试 -->
        <div class="test-section">
          <h3 class="section-title">移动端尺寸测试 (300px × 400px)</h3>
          <div class="test-panel mobile-size">
            <ControlPanel title="移动端控制面板" />
          </div>
        </div>
      </div>

      <!-- 右侧面板 - 模拟 Dashboard 右侧布局 -->
      <div class="test-right-panels">
        <div class="test-panel">
          <h3 class="panel-title">右侧面板尺寸 (360px)</h3>
          <ControlPanel title="监控控制面板" />
        </div>
      </div>
    </div>

    <!-- 响应式测试说明 -->
    <div class="responsive-info">
      <h3>响应式测试说明</h3>
      <ul>
        <li><strong>1400px+:</strong> 完整布局，左侧340px，右侧360px</li>
        <li><strong>1200px-1400px:</strong> 中等布局，左侧220px，右侧240px</li>
        <li><strong>768px-1200px:</strong> 紧凑布局，左侧200px，右侧200px</li>
        <li><strong>768px以下:</strong> 移动端布局，左右侧180px，垂直排列</li>
      </ul>
    </div>

    <!-- 功能测试说明 -->
    <div class="feature-info">
      <h3>ControlPanel 组件功能说明</h3>
      <div class="feature-grid">
        <div class="feature-item">
          <h4>操作模式控制</h4>
          <p>支持自动/手动模式切换，手动模式下显示详细控制选项</p>
        </div>
        <div class="feature-item">
          <h4>设备状态监控</h4>
          <p>实时显示运行状态、变频器状态等关键设备信息</p>
        </div>
        <div class="feature-item">
          <h4>手动设备控制</h4>
          <p>手动模式下可控制风阀、除湿机、风机等设备的启停</p>
        </div>
        <div class="feature-item">
          <h4>参数设置</h4>
          <p>支持温度、湿度、运行模式、能耗等参数的设定</p>
        </div>
      </div>

      <div class="access-info">
        <h4>访问地址</h4>
        <p><strong>测试页面:</strong> <code>http://localhost:5173/test/control-panel</code></p>
        <p><strong>Dashboard集成:</strong> 组件已集成在主仪表板的左右侧面板中</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import ControlPanel from '@/components/ControlPanel.vue'
</script>

<style scoped>
.control-panel-test-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
  padding: 20px;
  color: #ffffff;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(0, 162, 255, 0.1);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
}

.test-title {
  color: #00a2ff;
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 10px;
}

.test-info {
  color: #8892b0;
  font-size: 16px;
}

.test-content {
  display: grid;
  grid-template-columns: 340px 1fr 360px;
  gap: 20px;
  margin-bottom: 30px;
  min-height: 600px;
}

/* 左侧面板测试 */
.test-left-panels {
  display: flex;
  flex-direction: column;
}

.test-left-panels .test-panel {
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  padding: 15px;
}

/* 右侧面板测试 */
.test-right-panels {
  display: flex;
  flex-direction: column;
}

.test-right-panels .test-panel {
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  padding: 15px;
}

/* 中间测试区域 */
.test-center-area {
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.test-section {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  padding: 15px;
}

.section-title {
  color: #00a2ff;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 15px;
  text-align: center;
}

.panel-title {
  color: #00a2ff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  text-align: center;
}

/* 不同尺寸的测试面板 */
.test-panel {
  margin: 0 auto;
}

.standard-size {
  width: 500px;
  height: 600px;
}

.compact-size {
  width: 400px;
  height: 500px;
}

.mobile-size {
  width: 300px;
  height: 400px;
}

/* 响应式测试说明 */
.responsive-info,
.feature-info {
  background: rgba(0, 162, 255, 0.1);
  border: 1px solid rgba(0, 162, 255, 0.3);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.responsive-info h3,
.feature-info h3 {
  color: #00a2ff;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
}

.responsive-info ul {
  list-style: none;
  padding: 0;
}

.responsive-info li {
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.responsive-info li::before {
  content: "•";
  color: #00a2ff;
  position: absolute;
  left: 0;
}

/* 功能说明样式 */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.feature-item {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(0, 162, 255, 0.2);
  border-radius: 6px;
  padding: 15px;
}

.feature-item h4 {
  color: #00a2ff;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
}

.feature-item p {
  color: #ffffff;
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
  opacity: 0.9;
}

.access-info {
  background: rgba(0, 255, 136, 0.1);
  border: 1px solid rgba(0, 255, 136, 0.3);
  border-radius: 6px;
  padding: 15px;
}

.access-info h4 {
  color: #00ff88;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 10px;
}

.access-info p {
  color: #ffffff;
  font-size: 14px;
  margin-bottom: 8px;
}

.access-info p:last-child {
  margin-bottom: 0;
}

.access-info code {
  background: rgba(0, 0, 0, 0.3);
  color: #00a2ff;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .test-content {
    grid-template-columns: 220px 1fr 240px;
  }
}

@media (max-width: 1200px) {
  .test-content {
    grid-template-columns: 200px 1fr 200px;
  }
  
  .standard-size {
    width: 400px;
    height: 500px;
  }
  
  .compact-size {
    width: 350px;
    height: 450px;
  }
  
  .mobile-size {
    width: 280px;
    height: 380px;
  }
}

@media (max-width: 768px) {
  .test-content {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .test-left-panels,
  .test-right-panels {
    order: 2;
  }
  
  .test-center-area {
    order: 1;
  }
  
  .test-left-panels .test-panel,
  .test-right-panels .test-panel {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
    height: 500px;
  }
  
  .standard-size,
  .compact-size,
  .mobile-size {
    width: 100%;
    max-width: 350px;
    height: 450px;
  }
}

@media (max-width: 480px) {
  .control-panel-test-container {
    padding: 10px;
  }

  .test-header {
    padding: 15px;
  }

  .test-title {
    font-size: 24px;
  }

  .test-info {
    font-size: 14px;
  }

  .test-left-panels .test-panel,
  .test-right-panels .test-panel,
  .standard-size,
  .compact-size,
  .mobile-size {
    height: 400px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }

  .responsive-info,
  .feature-info {
    padding: 15px;
  }

  .responsive-info h3,
  .feature-info h3 {
    font-size: 18px;
  }
}
</style>
