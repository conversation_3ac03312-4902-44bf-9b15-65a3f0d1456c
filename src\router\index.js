import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      requiresAuth: false
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      requiresAuth: true
    }
  },
  {
    path: '/charts',
    name: 'Charts',
    component: () => import('@/views/ChartView.vue'),
    meta: {
      requiresAuth: true
    }
  },
  {
    path: '/realtime/:id?',
    name: 'RealTime',
    component: () => import('@/views/RealTimeView.vue'),
    meta: {
      requiresAuth: false
    }
  },
  {
    path: '/test/chart',
    name: 'MonitorChartTest',
    component: () => import('@/views/test/MonitorChartTest.vue'),
    meta: {
      requiresAuth: false
    }
  },
  {
    path: '/test/control-panel',
    name: 'ControlPanelTest',
    component: () => import('@/views/test/ControlPanelTest.vue'),
    meta: {
      requiresAuth: false
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth && !userStore.isAuthenticated) {
    // 需要认证但未登录，跳转到登录页
    next('/login')
  } else if (to.path === '/login' && userStore.isAuthenticated) {
    // 已登录用户访问登录页，跳转到主页
    next('/dashboard')
  } else {
    next()
  }
})

export default router
