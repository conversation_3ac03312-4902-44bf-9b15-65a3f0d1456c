# Modbus设备控制API文档

## 概述
本文档描述了青马大桥BIM系统中Modbus设备控制相关的API接口，主要用于Vue前端控制面板的设备控制和状态监控。

## 接口列表

### 1. 执行设备控制命令
**接口地址：** `POST /modbusSite/modbusCmd`

**功能描述：** 执行Modbus设备控制命令

**请求体示例：**
```json
{
  "pointId": 1,
  "value": 1,
  "cmdType": "WRITE",
  "description": "设置操作模式为自动"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "Modbus控制命令执行成功",
  "data": true
}
```

**请求参数说明：**
- `pointId` (Long): 点位ID，通过此ID可以查找到对应的站点、主站、从站、寄存器地址等信息
- `value` (Object): 写入值，支持数字和布尔值
- `cmdType` (String): 命令类型，READ-读取，WRITE-写入
- `description` (String): 命令描述，可选

### 2. 批量查询点位状态
**接口地址：** `POST /modbusSite/pointStatus`

**功能描述：** 批量查询多个点位的当前状态值

**请求体示例：**
```json
{
  "pointIds": [1, 2, 3, 4, 5]
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "pointId": 1,
      "pointName": "操作模式",
      "description": "设备操作模式：0-手动，1-自动",
      "value": 1,
      "functionCode": "3",
      "registerAddress": 1001,
      "success": true,
      "errorMessage": null
    },
    {
      "pointId": 2,
      "pointName": "运行状态",
      "description": "设备运行状态：0-停止，1-运行",
      "value": 1,
      "functionCode": "3",
      "registerAddress": 1002,
      "success": true,
      "errorMessage": null
    },
    {
      "pointId": 3,
      "pointName": "变频器启停",
      "description": "变频器启停状态：0-停止，1-启动",
      "value": 0,
      "functionCode": "3",
      "registerAddress": 1003,
      "success": false,
      "errorMessage": "设备通信超时"
    }
  ]
}
```

**请求参数说明：**
- `pointIds` (List<Long>): 点位ID列表，支持批量查询多个点位

**响应字段说明：**
- `pointId`: 点位ID
- `pointName`: 点位名称
- `description`: 点位描述
- `value`: 点位当前值
- `functionCode`: 功能码
- `registerAddress`: 寄存器地址
- `success`: 读取是否成功
- `errorMessage`: 错误信息（读取失败时）

## 点位配置说明

### 点位信息获取
通过`pointId`可以从`modbus_points`表中获取以下信息：
- `siteId`: 所属站点ID
- `masterId`: 主站ID
- `slaveId`: 从站ID
- `functionCode`: 功能码（1-COIL_STATUS, 2-INPUT_STATUS, 3-HOLDING_REGISTER, 4-INPUT_REGISTER）
- `registerAddress`: 寄存器地址
- `dataType`: 数据类型（2-TWO_BYTE_INT_UNSIGNED, 3-TWO_BYTE_INT_SIGNED）

### 功能码说明
| 功能码 | 类型 | 说明 | 支持操作 |
|--------|------|------|----------|
| 1 | COIL_STATUS | 线圈状态 | 读取、写入 |
| 2 | INPUT_STATUS | 输入状态 | 仅读取 |
| 3 | HOLDING_REGISTER | 保持寄存器 | 读取、写入 |
| 4 | INPUT_REGISTER | 输入寄存器 | 仅读取 |

### 数据类型说明
| 数据类型 | 说明 |
|----------|------|
| 2 | TWO_BYTE_INT_UNSIGNED（无符号16位整数） |
| 3 | TWO_BYTE_INT_SIGNED（有符号16位整数） |

## Vue前端使用示例

### 1. 批量查询点位状态
```javascript
// 批量查询点位状态
async getPointStatus(pointIds) {
  const requestData = {
    pointIds: pointIds
  };

  try {
    const response = await this.$http.post('/modbusSite/pointStatus', requestData);
    if (response.code === 200) {
      this.pointValues = response.data;
      // 处理返回的点位状态
      response.data.forEach(point => {
        if (point.success) {
          console.log(`点位${point.pointName}当前值: ${point.value}`);
        } else {
          console.error(`点位${point.pointName}读取失败: ${point.errorMessage}`);
        }
      });
    }
  } catch (error) {
    this.$message.error('批量查询点位状态失败');
  }
}

// 使用示例
// 查询多个关键点位的状态
this.getPointStatus([1, 2, 3, 4, 5]);
```

### 2. 设备控制操作
```javascript
// 通用设备控制方法
async executeModbusCmd(pointId, value, description) {
  const cmdData = {
    pointId: pointId,
    value: value,
    cmdType: "WRITE",
    description: description
  };

  try {
    const response = await this.$http.post('/modbusSite/modbusCmd', cmdData);
    if (response.code === 200) {
      this.$message.success('控制命令执行成功');
      // 刷新点位状态
      this.getPointStatus([pointId]);
    }
  } catch (error) {
    this.$message.error('控制命令执行失败');
  }
}

// 读取设备状态
async readModbusPoint(pointId, description) {
  const cmdData = {
    pointId: pointId,
    value: 0, // 读取操作时值可以为任意
    cmdType: "READ",
    description: description
  };

  try {
    const response = await this.$http.post('/modbusSite/modbusCmd', cmdData);
    if (response.code === 200) {
      this.$message.success('读取成功');
    }
  } catch (error) {
    this.$message.error('读取失败');
  }
}

// 使用示例
// 切换操作模式（假设pointId=1对应操作模式点位）
this.executeModbusCmd(1, 1, "切换到自动模式");

// 启动变频器（假设pointId=2对应变频器启停点位）
this.executeModbusCmd(2, 1, "启动变频器");

// 设置温度（假设pointId=3对应温度设定点位）
this.executeModbusCmd(3, 25, "设置温度为25℃");

// 读取设备状态（假设pointId=4对应运行状态点位）
this.readModbusPoint(4, "读取运行状态");
```

### 3. 综合使用示例
```javascript
// 完整的设备控制面板示例
export default {
  data() {
    return {
      pointValues: [],
      // 关键点位ID配置
      keyPointIds: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
    }
  },

  mounted() {
    // 初始化时获取关键点位状态
    this.initPointStatus();
    // 定时刷新状态
    this.startStatusPolling();
  },

  methods: {
    // 初始化点位状态
    async initPointStatus() {
      await this.getPointStatus(this.keyPointIds);
    },

    // 定时刷新状态
    startStatusPolling() {
      setInterval(() => {
        this.getPointStatus(this.keyPointIds);
      }, 5000); // 每5秒刷新一次
    },

    // 设备控制操作
    async controlDevice(pointId, value, description) {
      await this.executeModbusCmd(pointId, value, description);
      // 控制后立即刷新状态
      setTimeout(() => {
        this.getPointStatus([pointId]);
      }, 1000);
    }
  }
}
```

## 权限要求
- 查询点位状态：`qingma:modbusSite:query`
- 执行控制命令：`qingma:modbusSite:cmd`

## 注意事项
1. 所有控制操作都会记录操作日志
2. 建议在执行控制命令后刷新相关点位状态
3. 异常情况下会返回相应的错误信息
4. 控制命令执行具有实时性，请确保网络连接稳定
5. 批量查询点位状态时，单个点位失败不影响其他点位的查询结果
