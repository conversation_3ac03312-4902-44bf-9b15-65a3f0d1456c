<template>
  <div class="dashboard-container">
    <!-- 顶部标题栏 -->
    <div class="dashboard-header">
      <div class="header-title">
        <h1 class="dashboard-title">
          Dehumidification System for Main Cable of Tsing Ma Bridge
        </h1>
        <!-- <h5>Dehumidification system for the main cable of Tsing Ma Bridge</h5> -->
      </div>
      <div class="header-actions">
        <!-- <div class="nav-buttons">
          <el-button @click="goToRealTime" size="small" type="info">
            <el-icon><Monitor /></el-icon>
            实时监测
          </el-button>
          <el-button @click="goToCharts" size="small" type="info">
            <el-icon><TrendCharts /></el-icon>
            数据图表
          </el-button>
        </div> -->
        <span class="user-info">Welcome,{{ userStore.userInfo?.username }}</span>
        <el-button type="primary" @click="handleLogout">LogOut</el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="dashboard-content">
      <!-- 中央3D显示区域 -->
      <div class="main-display">
        <IframePanel :url="mainDisplayUrl" title="" class="main-iframe" />

        <!-- 下发图表监测区域 -->
        <div class="floating-cameras">
          <MonitorChart
            id="4"
            data-type="pressure"
            class="chart-panel"
            :chart-height="228"
            :real-time-enabled="true"
          />
          <MonitorChart
            id="1"
            data-type="humidity"
            class="chart-panel"
            :chart-height="228"
            :real-time-enabled="true"
          />
        </div>
      </div>

      <!-- 左侧监测面板 -->
      <div class="left-panels">
        <RealTimeMonitor
          title="Plant Room（No. 61）"
          sub-title="Humidity"
          icon-name="icon-humidity"
          id="3"
          class="monitor-panel"
          :thresholds="humidityThresholds"
          @click="goToSpecificMonitor('3')"
        />
        <ControlPanel title="Control Panel" class="monitor-panel" />
        <DeviceStatusMonitor title="Equipment Status" class="monitor-panel" />
      </div>

      <!-- 右侧图表面板 -->
      <div class="right-panels">
        <RealTimeMonitor
          title="Injection Sleeve（No. 57）"
          sub-title="Humidity"
          icon-name="icon-humidity"
          id="2"
          class="monitor-panel"
          :thresholds="humidityThresholds"
          @click="goToSpecificMonitor('2')"
        />
        <RealTimeMonitor
          title="Exhaust Sleeve（No. 49）"
          sub-title="Humidity"
          icon-name="icon-humidity"
          id="1"
          class="monitor-panel"
          :thresholds="humidityThresholds"
          @click="goToSpecificMonitor('1')"
        />
        <RealTimeMonitor
          title="Exhaust Sleeve（No. 66）"
          sub-title="Humidity"
          icon-name="icon-humidity"
          id="4"
          class="monitor-panel"
          :thresholds="humidityThresholds"
          @click="goToSpecificMonitor('4')"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useUserStore } from "@/stores/user";
import { ElMessage, ElMessageBox } from "element-plus";
import IframePanel from "@/components/IframePanel.vue";
import RealTimeMonitor from "@/components/RealTimeMonitor.vue";
import ControlPanel from "@/components/ControlPanel.vue";
import MonitorChart from "@/components/MonitorChart.vue";
import DeviceStatusMonitor from "@/components/DeviceStatusMonitor.vue";
import {ENV_CONFIG} from "@/utils/config.js";

const router = useRouter();
const userStore = useUserStore();

// 主显示区域URL（3D模型）
const mainDisplayUrl = ref(ENV_CONFIG.BIM_URL)

// 各监测面板的URL配置（保留摄像头URL）
const monitoringUrls = ref({
  // 摄像头
  camera1: "https://example.com/camera-1",
  camera2: "https://example.com/camera-2",
});

// 阈值配置
const humidityThresholds = ref({
  warning: { min: 30, max: 80 },
  danger: { min: 20, max: 90 },
});

const pressureThresholds = ref({
  warning: { min: 1005, max: 1020 },
  danger: { min: 1000, max: 1025 },
});

const temperatureThresholds = ref({
  warning: { min: 18, max: 28 },
  danger: { min: 15, max: 32 },
});

const flowThresholds = ref({
  warning: { min: 100, max: 150 },
  danger: { min: 80, max: 180 },
});

// 导航到实时监测页面
const goToRealTime = () => {
  router.push("/realtime");
};

// 导航到数据图表页面
const goToCharts = () => {
  router.push("/charts");
};

// 导航到特定监测器
const goToSpecificMonitor = (id) => {
  router.push(`/realtime/${id}`);
};

const handleLogout = async () => {
  try {
    await ElMessageBox.confirm("确定要退出登录吗？", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    userStore.logout();
    ElMessage.success("已退出登录");
    router.push("/login");
  } catch {
    // 用户取消
  }
};

onMounted(() => {
  // 初始化用户信息
  userStore.initUserInfo();
});
</script>

<style scoped>
.dashboard-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0c1426 0%, #1a2332 50%, #0f1419 100%);
  overflow: hidden;
  position: relative;
}

.dashboard-header {
  display: flex;
  justify-content: center;
  height: 80px;
  padding: 18px 30px;
  background-image: url("@/assets/bt_bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  border-bottom: 1px solid rgba(0, 162, 255, 0.3);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 100;
  opacity: 0.9;
}

.dashboard-title {
  font-size: 26px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
  text-shadow: 0 0 10px rgba(0, 162, 255, 0.3);
  letter-spacing: 1px;
  margin-top: -5px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
  position: absolute;
  right: 30px;
}

.nav-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  color: #ffffff;
  font-size: 14px;
}

.dashboard-content {
  position: relative;
  width: 100%;
  height: 100vh;
  padding: 0;
}

.main-display {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.main-iframe {
  width: 100%;
  height: 100%;
}

/* 悬浮摄像机容器 */
.floating-cameras {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 20;
  display: flex;
  align-items: flex-end;
  gap: 15px;
  width: 31%
}

.left-panels {
  position: absolute;
  top: 100px;
  left: 20px;
  width: 340px;
  height: calc(100% - 120px);
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 10;
}

.right-panels {
  position: absolute;
  top: 100px;
  right: 20px;
  width: 360px;
  height: calc(100% - 120px);
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 10;
}

.monitor-panel,
.chart-panel {
  flex: 1;
  min-height: 0;
}

.floating-camera-panel {
  width: 340px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: height 0.3s ease;
}

.floating-camera-panel:not(.collapsed) {
  height: 280px;
}

.floating-camera-panel.collapsed {
  height: 35px;
  max-height: 35px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .left-panels {
    width: 220px;
  }

  .right-panels {
    width: 240px;
  }

  .floating-camera-panel {
    width: 180px;
    max-height: 100px;
  }

  .floating-camera-panel:not(.collapsed) {
    height: 100px;
  }

  .floating-camera-panel.collapsed {
    height: 35px;
    max-height: 35px;
  }

  .floating-cameras {
    gap: 10px;
  }
}

@media (max-width: 1200px) {
  .left-panels {
    width: 200px;
  }

  .right-panels {
    width: 200px;
  }

  .dashboard-header {
    padding: 10px 20px;
  }

  .floating-camera-panel {
    width: 160px;
    max-height: 90px;
  }

  .floating-camera-panel:not(.collapsed) {
    height: 90px;
  }

  .floating-camera-panel.collapsed {
    height: 35px;
    max-height: 35px;
  }

  .floating-cameras {
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .left-panels {
    width: 180px;
    top: 90px;
    left: 10px;
    height: calc(50% - 100px);
  }

  .right-panels {
    width: 180px;
    top: calc(50% + 10px);
    right: 10px;
    height: calc(50% - 20px);
  }

  .floating-cameras {
    bottom: 10px;
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }

  .floating-camera-panel {
    width: 150px;
    max-height: 90px;
  }

  .floating-camera-panel:not(.collapsed) {
    height: 90px;
  }

  .floating-camera-panel.collapsed {
    height: 40px;
    max-height: 40px;
  }
}
</style>
